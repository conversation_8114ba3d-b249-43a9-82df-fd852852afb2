2025-07-01 11:11:12 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 11:11:12 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 11:11:12 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 11:11:12 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 11:11:12 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 11:11:12 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | memory_available_gb: 21.57
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | gpu_available: True
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | gpu_count: 1
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | gpu_name: NVIDIA GeForce RTX 4090
2025-07-01 11:11:12 | INFO     | __main__:check_environment:58 | gpu_memory_gb: 23.99
2025-07-01 11:11:12 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 11:11:12 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 11:11:12 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 11:11:12 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 11:11:12 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 11:11:12 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 11:11:12 | INFO     | __main__:main:98 | 检测到打包环境，跳过依赖检查
2025-07-01 11:11:12 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 11:11:12 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 11:11:12 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 11:11:12 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\LocalQA_交付包\data
2025-07-01 11:11:12 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\LocalQA_交付包\models
2025-07-01 11:11:12 | WARNING  | __main__:main:117 | 目录不存在，将创建: D:\LocalQA\LocalQA_交付包\docs
2025-07-01 11:11:12 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 11:11:18 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 11:11:18 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 11:11:18 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 11:11:18 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 11:11:18 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 新对话
2025-07-01 11:11:18 | INFO     | src.core.session_manager:_load_or_create_default_session:96 | 创建默认会话
2025-07-01 11:11:18 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 新对话, ID: ffa38613-3c6e-4202-b7e9-f278ab47faae, 数据: session_ffa38613-3c6e-4202-b7e9-f278ab47faae
2025-07-01 11:11:18 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 新对话
2025-07-01 11:11:18 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 新对话
2025-07-01 11:11:19 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 11:11:19 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 11:11:19 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 11:11:19 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 11:11:19 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 11:11:19 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 11:11:19 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 11:11:19 | WARNING  | src.core.vector_store:_initialize_client:94 | 新版本ChromaDB初始化失败，尝试兼容模式: No module named 'chromadb.telemetry.product.posthog'
2025-07-01 11:11:19 | WARNING  | src.core.vector_store:_initialize_client:101 | 基本ChromaDB初始化失败，尝试内存模式: An instance of Chroma already exists for D:\LocalQA\LocalQA_交付包\data\chroma_db with different settings
2025-07-01 11:11:19 | ERROR    | src.core.vector_store:_initialize_client:119 | ChromaDB初始化失败: No module named 'chromadb.telemetry.product.posthog'
2025-07-01 11:11:19 | WARNING  | src.core.vector_store:_initialize_client:123 | 向量存储初始化失败，将禁用语义搜索功能
2025-07-01 11:11:19 | INFO     | src.core.vector_store:_load_embedding_model:139 | 下载嵌入模型: shibing624/text2vec-base-chinese
