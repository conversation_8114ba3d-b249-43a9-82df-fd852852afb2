2025-07-01 05:20:28 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:20:28 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:20:28 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:20:28 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 05:20:28 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 05:20:28 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | memory_available_gb: 22.73
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | gpu_available: True
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | gpu_count: 1
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | gpu_name: NVIDIA GeForce RTX 4090
2025-07-01 05:20:28 | INFO     | __main__:check_environment:58 | gpu_memory_gb: 23.99
2025-07-01 05:20:28 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 05:20:28 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 05:20:28 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 05:20:28 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 05:20:28 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 05:20:28 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 05:20:28 | INFO     | __main__:main:93 | 检查Python依赖...
2025-07-01 05:20:28 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 05:20:28 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: True
2025-07-01 05:20:28 | INFO     | src.utils.dependency_checker:_check_torch:252 | 可用GPU数量: 1
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.1.2+cu118)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.22)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.14)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.0)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 05:20:33 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 05:20:33 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 05:20:33 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 05:20:33 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 05:20:33 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 05:20:33 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 05:20:33 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 05:20:34 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 05:20:34 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 05:20:34 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 新对话
2025-07-01 05:20:34 | INFO     | src.core.session_manager:_load_or_create_default_session:96 | 创建默认会话
2025-07-01 05:20:34 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 新对话, ID: cd702d76-26ac-41c4-848d-a1a915a0c149, 数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 05:20:34 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 新对话
2025-07-01 05:20:34 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 新对话
2025-07-01 05:20:34 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 05:20:34 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 05:20:34 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 05:20:34 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 05:20:34 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 05:20:34 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 05:20:34 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:20:34 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:20:34 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:20:34 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:20:34 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 05:20:34 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:20:34 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 05:20:34 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:20:34 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 05:20:34 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 05:20:34 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 05:20:34 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 05:20:34 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 05:20:34 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 05:20:46 | INFO     | src.core.search_system:ai_question:365 | 开始AI问答: 业务招待费的标准
2025-07-01 05:20:47 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:20:47 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费的标准, 返回 5 个结果
2025-07-01 05:20:47 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 05:20:47 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 1182.3MB (3.7%)
2025-07-01 05:20:47 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 05:20:47 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 05:20:50 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 05:20:50 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.94秒
2025-07-01 05:20:50 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 455.0MB
2025-07-01 05:20:50 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 05:20:50 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 05:20:50 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 05:20:50 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:20:51 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 0.99秒
2025-07-01 05:20:51 | INFO     | src.core.search_system:ai_question:409 | AI问答完成: 业务招待费的标准, 耗时 4.96秒
2025-07-01 05:20:51 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 05:21:08 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:538 | 🔗 来源链接点击: source_1
2025-07-01 05:21:08 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:548 | 📍 解析来源索引: 0
2025-07-01 05:21:08 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:554 | 📝 当前会话消息数量: 2
2025-07-01 05:21:08 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:560 | ✅ 找到AI消息，来源数量: 5
2025-07-01 05:21:08 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:565 | 🎯 显示来源 1: <class 'dict'>
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:load_document:406 | 加载文档: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf, 页码: 3
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:load_document:422 | 检测到PDF文件，尝试直接加载: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:load_document:427 | 使用QWebEngineView直接加载PDF
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:_load_pdf_directly:480 | 使用PDF.js加载PDF: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:_load_pdf_directly:481 | PDF.js查看器路径: file:///D:/LocalQA/static/pdfjs/pdf_viewer.html
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 05:21:08 | INFO     | src.ui.preview_widget:_send_pdf_to_viewer:557 | 已发送PDF加载指令: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf, 页码: 3
2025-07-01 05:21:11 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:21:11 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:21:12 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:21:12 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 05:21:12 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 350.5MB (1.1%)
2025-07-01 05:21:12 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 05:21:12 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 05:21:14 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 05:21:14 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.17秒
2025-07-01 05:21:14 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 560.7MB
2025-07-01 05:21:14 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 05:21:14 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 05:21:15 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 05:21:20 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 05:21:21 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
2025-07-01 05:22:28 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:22:28 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:22:30 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:22:30 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:22:30 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:22:31 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:22:31 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:25:48 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:25:48 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:25:48 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:25:48 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 05:25:48 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:25:48 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:25:48 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:25:48 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:25:48 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 05:25:48 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:25:48 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 05:25:48 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:25:48 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 05:25:48 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 05:25:49 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 3 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全管理, 返回 3 个结果
2025-07-01 05:25:49 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '安全管理', 返回 3 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 安全管理, 返回 3 个结果
2025-07-01 05:25:49 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全管理, 返回 1 个结果
2025-07-01 05:25:49 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '安全管理', 返回 1 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 安全管理, 返回 1 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 安全管理, 返回 2 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:ai_question:365 | 开始AI问答: 公司的安全管理制度有哪些？
2025-07-01 05:25:49 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:25:49 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 公司的安全管理制度有哪些？, 返回 5 个结果
2025-07-01 05:25:49 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 05:25:49 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 992.9MB (3.1%)
2025-07-01 05:25:49 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 05:25:49 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 05:25:51 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 05:25:51 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.00秒
2025-07-01 05:25:51 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 456.7MB
2025-07-01 05:25:51 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 05:25:51 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 05:25:51 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 05:25:51 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 05:25:51 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 05:25:51 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:25:52 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.05秒
2025-07-01 05:25:52 | INFO     | src.core.search_system:ai_question:409 | AI问答完成: 公司的安全管理制度有哪些？, 耗时 3.24秒
2025-07-01 05:25:52 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:25:52 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:25:52 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:25:52 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:25:53 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 3 个结果
2025-07-01 05:25:53 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:25:53 | ERROR    | src.core.vector_store:search:243 | 语义搜索失败: Number of requested results 0, cannot be negative, or zero.
2025-07-01 05:25:53 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.text_index:search:225 | 全文搜索失败: limit must be >= 1
2025-07-01 05:25:53 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 安全, 返回 0 个结果
2025-07-01 05:25:53 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 安全, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.vector_store:search:243 | 语义搜索失败: Number of requested results 0, cannot be negative, or zero.
2025-07-01 05:25:53 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 管理, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.text_index:search:225 | 全文搜索失败: limit must be >= 1
2025-07-01 05:25:53 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 管理, 返回 0 个结果
2025-07-01 05:25:53 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 管理, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.vector_store:search:243 | 语义搜索失败: Number of requested results 0, cannot be negative, or zero.
2025-07-01 05:25:53 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 制度, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.text_index:search:225 | 全文搜索失败: limit must be >= 1
2025-07-01 05:25:53 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 制度, 返回 0 个结果
2025-07-01 05:25:53 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 制度, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.vector_store:search:243 | 语义搜索失败: Number of requested results 0, cannot be negative, or zero.
2025-07-01 05:25:53 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 船舶, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.text_index:search:225 | 全文搜索失败: limit must be >= 1
2025-07-01 05:25:53 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 船舶, 返回 0 个结果
2025-07-01 05:25:53 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 船舶, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.vector_store:search:243 | 语义搜索失败: Number of requested results 0, cannot be negative, or zero.
2025-07-01 05:25:53 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 财务, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.text_index:search:225 | 全文搜索失败: limit must be >= 1
2025-07-01 05:25:53 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 财务, 返回 0 个结果
2025-07-01 05:25:53 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 财务, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.vector_store:search:243 | 语义搜索失败: Number of requested results 0, cannot be negative, or zero.
2025-07-01 05:25:53 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 培训, 返回 0 个结果
2025-07-01 05:25:53 | ERROR    | src.core.text_index:search:225 | 全文搜索失败: limit must be >= 1
2025-07-01 05:25:53 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 培训, 返回 0 个结果
2025-07-01 05:25:53 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 培训, 返回 0 个结果
2025-07-01 05:27:16 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:27:16 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:27:16 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:27:16 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 05:27:16 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:27:16 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:27:16 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:27:16 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:27:16 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 05:27:16 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:27:16 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 05:27:16 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:27:16 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 05:27:16 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 05:27:17 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '安全', 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 安全, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 安全, 返回 2 个结果
2025-07-01 05:27:17 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 管理, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '管理', 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 管理, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 管理, 返回 2 个结果
2025-07-01 05:27:17 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 制度, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '制度', 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 制度, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 制度, 返回 2 个结果
2025-07-01 05:27:17 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 船舶, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '船舶', 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 船舶, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 船舶, 返回 2 个结果
2025-07-01 05:27:17 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 财务, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '财务', 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 财务, 返回 1 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:hybrid_search:353 | 混合搜索完成: 财务, 返回 2 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:ai_question:365 | 开始AI问答: 公司有哪些安全制度？
2025-07-01 05:27:17 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:27:17 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 公司有哪些安全制度？, 返回 5 个结果
2025-07-01 05:27:17 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 05:27:17 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 994.2MB (3.1%)
2025-07-01 05:27:17 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 05:27:17 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 05:27:19 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 05:27:19 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.04秒
2025-07-01 05:27:19 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 453.7MB
2025-07-01 05:27:19 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 05:27:19 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 05:27:19 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 05:27:19 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 05:27:19 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 05:27:19 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:27:20 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.09秒
2025-07-01 05:27:20 | INFO     | src.core.search_system:ai_question:409 | AI问答完成: 公司有哪些安全制度？, 耗时 3.34秒
2025-07-01 05:27:21 | INFO     | src.core.search_system:ai_question:365 | 开始AI问答: 差旅费报销标准是什么？
2025-07-01 05:27:21 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:27:21 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 差旅费报销标准是什么？, 返回 5 个结果
2025-07-01 05:27:21 | INFO     | src.core.ai_model:chat:537 | 提示词token数量: 419
2025-07-01 05:27:21 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:27:22 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.08秒
2025-07-01 05:27:22 | INFO     | src.core.search_system:ai_question:409 | AI问答完成: 差旅费报销标准是什么？, 耗时 1.13秒
2025-07-01 05:27:22 | INFO     | src.core.search_system:ai_question:365 | 开始AI问答: 船舶管理有什么规定？
2025-07-01 05:27:22 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:27:22 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 船舶管理有什么规定？, 返回 5 个结果
2025-07-01 05:27:22 | INFO     | src.core.ai_model:chat:537 | 提示词token数量: 318
2025-07-01 05:27:22 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:27:23 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 0.97秒
2025-07-01 05:27:23 | INFO     | src.core.search_system:ai_question:409 | AI问答完成: 船舶管理有什么规定？, 耗时 1.00秒
2025-07-01 05:35:56 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:35:56 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:35:56 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:35:57 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 05:35:57 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:35:57 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:35:57 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:35:57 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:35:57 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 05:35:57 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:35:57 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 05:35:57 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:35:57 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 05:35:57 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 05:35:57 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:35:57 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '安全', 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 安全, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 安全, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 管理, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '管理', 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 管理, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 管理, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 制度, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '制度', 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 制度, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 制度, 返回 1 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 公司有哪些安全制度？
2025-07-01 05:35:58 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:35:58 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 公司有哪些安全制度？, 返回 5 个结果
2025-07-01 05:35:58 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 05:35:58 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 993.1MB (3.1%)
2025-07-01 05:35:58 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 05:35:58 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 05:36:00 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 05:36:00 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.06秒
2025-07-01 05:36:00 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 454.6MB
2025-07-01 05:36:00 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 05:36:00 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 05:36:00 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 05:36:00 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 05:36:00 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 05:36:00 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:36:01 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.12秒
2025-07-01 05:36:01 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 公司有哪些安全制度？, 耗时 3.36秒
2025-07-01 05:36:01 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 差旅费报销标准是什么？
2025-07-01 05:36:01 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:36:01 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 差旅费报销标准是什么？, 返回 5 个结果
2025-07-01 05:36:01 | INFO     | src.core.ai_model:chat:537 | 提示词token数量: 419
2025-07-01 05:36:01 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:36:02 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.03秒
2025-07-01 05:36:02 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 差旅费报销标准是什么？, 耗时 1.07秒
2025-07-01 05:40:26 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:40:26 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 05:40:26 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 05:40:27 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 05:40:27 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:40:27 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 05:40:27 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 05:40:27 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 05:40:27 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 05:40:27 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 05:40:27 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 05:40:27 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 05:40:27 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 05:40:27 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 05:40:27 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 3 个结果
2025-07-01 05:40:27 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全, 返回 3 个结果
2025-07-01 05:40:27 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 公司有哪些安全制度？
2025-07-01 05:40:27 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 05:40:27 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 公司有哪些安全制度？, 返回 5 个结果
2025-07-01 05:40:27 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 05:40:27 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 926.0MB (2.9%)
2025-07-01 05:40:27 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 05:40:27 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 05:40:29 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 05:40:29 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.03秒
2025-07-01 05:40:29 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 455.4MB
2025-07-01 05:40:29 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 05:40:29 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 05:40:29 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 05:40:29 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 05:40:29 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 05:40:29 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 05:40:31 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.12秒
2025-07-01 05:40:31 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 公司有哪些安全制度？, 耗时 3.29秒
2025-07-01 06:06:29 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 06:06:29 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 06:06:29 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 06:06:30 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 06:06:30 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 06:06:30 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 06:06:30 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 06:06:30 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 06:06:30 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 06:06:30 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 06:06:30 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 06:06:30 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 06:06:30 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 06:06:30 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 06:06:30 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 06:06:30 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全, 返回 1 个结果
2025-07-01 06:06:30 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 06:06:30 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 06:06:30 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 06:06:30 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 06:06:31 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 06:06:31 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 06:06:31 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 06:06:31 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 06:06:31 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 06:06:31 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 06:06:31 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 3 个结果
2025-07-01 06:06:31 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 安全, 返回 3 个结果
2025-07-01 06:06:31 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 公司有哪些安全制度？
2025-07-01 06:06:31 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 06:06:31 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 公司有哪些安全制度？, 返回 5 个结果
2025-07-01 06:06:31 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 06:06:31 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 929.4MB (2.9%)
2025-07-01 06:06:31 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 06:06:31 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 06:06:33 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 06:06:33 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.06秒
2025-07-01 06:06:33 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 455.7MB
2025-07-01 06:06:33 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 06:06:33 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 06:06:33 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 06:06:33 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 06:06:33 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 06:06:33 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 06:06:34 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.04秒
2025-07-01 06:06:34 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 公司有哪些安全制度？, 耗时 3.23秒
2025-07-01 06:06:34 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 管理, 返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 制度, 返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 船舶, 返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 1 个结果
2025-07-01 06:06:34 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 财务, 返回 1 个结果
2025-07-01 08:59:02 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 08:59:02 | INFO     | src.utils.config:_validate_config:317 | 配置验证通过
2025-07-01 08:59:02 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 08:59:02 | INFO     | __main__:main:84 | === 公司制度本地查询平台启动 ===
2025-07-01 08:59:02 | INFO     | src.utils.helpers:check_system_requirements:103 | 系统检查结果: {'memory_sufficient': True, 'python_version_ok': True, 'windows_system': True, 'torch_available': True, 'psutil_available': True}
2025-07-01 08:59:02 | INFO     | __main__:check_environment:56 | === 系统信息 ===
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | platform: Windows-10-10.0.22631-SP0
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | system: Windows
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | machine: AMD64
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | processor: AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | python_version: 3.10.11
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | memory_total_gb: 31.63
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | memory_available_gb: 23.24
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | cpu_count: 16
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | gpu_available: True
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | gpu_count: 1
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | gpu_name: NVIDIA GeForce RTX 4090
2025-07-01 08:59:02 | INFO     | __main__:check_environment:58 | gpu_memory_gb: 23.99
2025-07-01 08:59:02 | INFO     | __main__:check_environment:60 | === 系统要求检查 ===
2025-07-01 08:59:02 | INFO     | __main__:check_environment:63 | ✓ memory_sufficient: True
2025-07-01 08:59:02 | INFO     | __main__:check_environment:63 | ✓ python_version_ok: True
2025-07-01 08:59:02 | INFO     | __main__:check_environment:63 | ✓ windows_system: True
2025-07-01 08:59:02 | INFO     | __main__:check_environment:63 | ✓ torch_available: True
2025-07-01 08:59:02 | INFO     | __main__:check_environment:63 | ✓ psutil_available: True
2025-07-01 08:59:02 | INFO     | __main__:main:93 | 检查Python依赖...
2025-07-01 08:59:02 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 08:59:02 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: True
2025-07-01 08:59:02 | INFO     | src.utils.dependency_checker:_check_torch:252 | 可用GPU数量: 1
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.1.2+cu118)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.22)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.14)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.0)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 08:59:07 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 08:59:07 | INFO     | __main__:main:102 | 配置加载完成: 公司制度查询平台
2025-07-01 08:59:07 | INFO     | __main__:main:105 | 启动性能监控...
2025-07-01 08:59:07 | INFO     | src.utils.performance_monitor:start_monitoring:69 | 性能监控已启动，监控间隔: 10.0秒
2025-07-01 08:59:07 | INFO     | __main__:main:124 | PyQt6导入成功
2025-07-01 08:59:08 | INFO     | __main__:main:131 | 主窗口模块导入成功
2025-07-01 08:59:08 | INFO     | __main__:main:139 | OpenGL上下文设置成功
2025-07-01 08:59:08 | INFO     | src.core.device_manager:_detect_devices:59 | 检测到GPU: NVIDIA GeForce RTX 4090, 显存: 24.0GB
2025-07-01 08:59:08 | INFO     | src.core.device_manager:_detect_devices:63 | CPU核心数: 16
2025-07-01 08:59:08 | INFO     | src.core.session_manager:_load_or_create_default_session:92 | 加载最近会话: 业务招待费的标准
2025-07-01 08:59:08 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费的标准, ID: cd702d76-26ac-41c4-848d-a1a915a0c149, 数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 08:59:08 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费的标准
2025-07-01 08:59:08 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费的标准
2025-07-01 08:59:08 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:218 | 开始创建对话面板
2025-07-01 08:59:08 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:222 | 创建主分割器成功
2025-07-01 08:59:08 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:325 | 聊天区域创建成功
2025-07-01 08:59:09 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:342 | 预览区域创建成功
2025-07-01 08:59:09 | INFO     | src.ui.enhanced_qa_interface:_create_chat_panel:351 | 主分割器添加到布局成功
2025-07-01 08:59:09 | INFO     | src.core.search_system:_initialize_components:168 | 初始化搜索系统组件...
2025-07-01 08:59:09 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 08:59:09 | INFO     | src.core.vector_store:_initialize_client:110 | 加载现有集合: policy_documents
2025-07-01 08:59:09 | INFO     | src.core.vector_store:_load_embedding_model:136 | 加载本地嵌入模型: D:\LocalQA\models\shibing624\text2vec-base-chinese
2025-07-01 08:59:09 | INFO     | src.core.vector_store:_load_embedding_model:147 | 嵌入模型加载成功，设备: cuda
2025-07-01 08:59:09 | INFO     | src.core.search_system:_initialize_components:172 | 向量存储初始化完成
2025-07-01 08:59:09 | INFO     | src.core.text_index:_initialize_index:123 | 加载现有索引: D:\LocalQA\data\whoosh_index
2025-07-01 08:59:09 | INFO     | src.core.search_system:_initialize_components:176 | 全文索引初始化完成
2025-07-01 08:59:09 | INFO     | src.utils.helpers:get_optimal_device:114 | 使用GPU设备，显存: 24.0GB
2025-07-01 08:59:09 | INFO     | src.core.search_system:_initialize_components:180 | AI模型管理器初始化完成
2025-07-01 08:59:09 | INFO     | src.core.search_system:_initialize_components:182 | 搜索系统初始化完成
2025-07-01 08:59:09 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 08:59:09 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 08:59:09 | INFO     | src.ui.main_window:_init_ui:178 | 风格主界面初始化完成
2025-07-01 08:59:09 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 搜索系统已就绪
2025-07-01 08:59:09 | INFO     | __main__:main:152 | GUI界面启动成功
2025-07-01 08:59:14 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 🔍 制度检索, 类型: search_root
2025-07-01 08:59:14 | INFO     | src.ui.main_window:_on_nav_item_clicked:480 | 切换到制度检索
2025-07-01 08:59:14 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 制度检索
2025-07-01 08:59:16 | INFO     | src.core.device_manager:_apply_cpu_optimization:107 | 应用CPU性能优化...
2025-07-01 08:59:16 | INFO     | src.core.device_manager:_apply_cpu_optimization:134 | PyTorch线程数设置为: 8
2025-07-01 08:59:16 | INFO     | src.core.device_manager:_apply_cpu_optimization:147 | Intel MKL优化已启用
2025-07-01 08:59:16 | INFO     | src.core.device_manager:_apply_cpu_optimization:152 | Intel MKL-DNN优化已启用
2025-07-01 08:59:16 | INFO     | src.core.device_manager:_apply_cpu_optimization:158 | CPU优化完成: 使用8个线程
2025-07-01 08:59:16 | INFO     | src.core.device_manager:switch_device:98 | 设备已切换到: cpu
2025-07-01 08:59:16 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 08:59:16 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 998.9MB (3.1%)
2025-07-01 08:59:16 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 08:59:16 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 08:59:18 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 08:59:18 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.10秒
2025-07-01 08:59:18 | INFO     | src.core.ai_model:load_model:222 | 内存增加: 7824.3MB
2025-07-01 08:59:18 | INFO     | src.core.ai_model:load_model:223 | 设备: cpu
2025-07-01 08:59:18 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 08:59:19 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 08:59:25 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 25 个结果
2025-07-01 08:59:25 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费, 返回 25 个结果
2025-07-01 08:59:25 | INFO     | src.core.text_index:search:221 | 全文搜索完成，查询: '业务招待费', 返回 6 个结果
2025-07-01 08:59:25 | INFO     | src.core.search_system:text_search:302 | 全文搜索完成: 业务招待费, 返回 6 个结果
2025-07-01 08:59:25 | INFO     | src.core.search_system:hybrid_search:357 | 混合搜索完成: 业务招待费, 返回 7 个结果
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 业务招待费的标准, 类型: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149, 提取的会话ID: cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 08:59:27 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 业务招待费的标准
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费的标准, ID: cd702d76-26ac-41c4-848d-a1a915a0c149, 数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 业务招待费的标准
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 业务招待费的标准
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 业务招待费的标准
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_switch_to_session:652 | 当前页面: 1, 问答界面存在: True
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 08:59:27 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 08:59:33 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 业务招待费的标准
2025-07-01 08:59:33 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 08:59:33 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 业务招待费的标准, 返回 5 个结果
2025-07-01 08:59:33 | INFO     | src.core.ai_model:chat:537 | 提示词token数量: 793
2025-07-01 08:59:33 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 08:59:56 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 22.97秒
2025-07-01 08:59:56 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 业务招待费的标准, 耗时 23.08秒
2025-07-01 08:59:56 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:00:04 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:538 | 🔗 来源链接点击: source_1
2025-07-01 09:00:04 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:548 | 📍 解析来源索引: 0
2025-07-01 09:00:04 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:554 | 📝 当前会话消息数量: 4
2025-07-01 09:00:04 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:560 | ✅ 找到AI消息，来源数量: 5
2025-07-01 09:00:04 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:565 | 🎯 显示来源 1: <class 'dict'>
2025-07-01 09:00:04 | INFO     | src.ui.preview_widget:load_document:406 | 加载文档: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf, 页码: 3
2025-07-01 09:00:04 | INFO     | src.ui.preview_widget:load_document:422 | 检测到PDF文件，尝试直接加载: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf
2025-07-01 09:00:04 | INFO     | src.ui.preview_widget:load_document:427 | 使用QWebEngineView直接加载PDF
2025-07-01 09:00:04 | INFO     | src.ui.preview_widget:_load_pdf_directly:480 | 使用PDF.js加载PDF: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf
2025-07-01 09:00:04 | INFO     | src.ui.preview_widget:_load_pdf_directly:481 | PDF.js查看器路径: file:///D:/LocalQA/static/pdfjs/pdf_viewer.html
2025-07-01 09:00:05 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:00:05 | INFO     | src.ui.preview_widget:_send_pdf_to_viewer:557 | 已发送PDF加载指令: D:\LocalQA\docs\总经办制度\沪港船办发[2023]048号-关于修订下发《上海港复兴船务有限公司业务招待管理规定》的通知.pdf, 页码: 3
2025-07-01 09:00:09 | INFO     | src.core.ai_model:unload_model:935 | 卸载AI模型...
2025-07-01 09:00:09 | INFO     | src.core.ai_model:unload_model:937 | 模型已卸载
2025-07-01 09:00:09 | INFO     | src.core.device_manager:_apply_gpu_optimization:162 | 应用GPU性能优化...
2025-07-01 09:00:09 | INFO     | src.core.device_manager:_apply_gpu_optimization:172 | GPU优化完成
2025-07-01 09:00:09 | INFO     | src.core.device_manager:switch_device:98 | 设备已切换到: cuda
2025-07-01 09:00:09 | INFO     | src.core.ai_model:load_model:141 | 开始加载AI模型: qwen-1.5-1.8b-chat
2025-07-01 09:00:09 | INFO     | src.core.ai_model:load_model:156 | 当前内存使用: 9453.2MB (29.2%)
2025-07-01 09:00:09 | INFO     | src.core.ai_model:load_model:170 | 加载tokenizer...
2025-07-01 09:00:09 | INFO     | src.core.ai_model:load_model:181 | 加载模型...
2025-07-01 09:00:11 | INFO     | src.core.ai_model:load_model:220 | 模型加载成功！
2025-07-01 09:00:11 | INFO     | src.core.ai_model:load_model:221 | 加载时间: 2.05秒
2025-07-01 09:00:11 | INFO     | src.core.ai_model:load_model:222 | 内存增加: -1951.4MB
2025-07-01 09:00:11 | INFO     | src.core.ai_model:load_model:223 | 设备: cuda
2025-07-01 09:00:11 | INFO     | src.core.ai_model:_warmup_model:251 | 开始模型预热...
2025-07-01 09:00:11 | INFO     | src.core.ai_model:_warmup_model:293 | 模型预热完成
2025-07-01 09:00:20 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 职工用餐标准
2025-07-01 09:00:20 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 09:00:20 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 职工用餐标准, 返回 5 个结果
2025-07-01 09:00:20 | INFO     | src.core.ai_model:chat:537 | 提示词token数量: 473
2025-07-01 09:00:20 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 09:00:20 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 0.69秒
2025-07-01 09:00:21 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 职工用餐标准, 耗时 0.79秒
2025-07-01 09:00:21 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: ➕ 新建对话, 类型: new_chat
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_on_nav_item_clicked:472 | 创建新会话
2025-07-01 09:00:27 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 对话 07-01 09:00
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 09:00, ID: 6bb992c4-a669-4d68-8882-2c2be30c796e, 数据: session_6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 09:00
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费的标准, ID: cd702d76-26ac-41c4-848d-a1a915a0c149, 数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 09:00
2025-07-01 09:00:27 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 09:00, ID: 6bb992c4-a669-4d68-8882-2c2be30c796e, 数据: session_6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 09:00
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费的标准, ID: cd702d76-26ac-41c4-848d-a1a915a0c149, 数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 09:00:27 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 09:00
2025-07-01 09:00:27 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:00:29 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 职工用餐标准
2025-07-01 09:00:29 | INFO     | src.core.search_system:ai_question:375 | 从缓存返回AI问答结果: 职工用餐标准
2025-07-01 09:00:29 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:00:34 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:538 | 🔗 来源链接点击: source_3
2025-07-01 09:00:34 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:548 | 📍 解析来源索引: 2
2025-07-01 09:00:34 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:554 | 📝 当前会话消息数量: 2
2025-07-01 09:00:34 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:560 | ✅ 找到AI消息，来源数量: 5
2025-07-01 09:00:34 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:565 | 🎯 显示来源 3: <class 'dict'>
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:load_document:406 | 加载文档: D:\LocalQA\docs\总经办制度\沪港船办发[2023]047号-关于修订下发《上海港复兴船务有限公司职工就餐管理规定》的通知.pdf, 页码: 3
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:load_document:422 | 检测到PDF文件，尝试直接加载: D:\LocalQA\docs\总经办制度\沪港船办发[2023]047号-关于修订下发《上海港复兴船务有限公司职工就餐管理规定》的通知.pdf
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:load_document:427 | 使用QWebEngineView直接加载PDF
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:_load_pdf_directly:480 | 使用PDF.js加载PDF: D:\LocalQA\docs\总经办制度\沪港船办发[2023]047号-关于修订下发《上海港复兴船务有限公司职工就餐管理规定》的通知.pdf
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:_load_pdf_directly:481 | PDF.js查看器路径: file:///D:/LocalQA/static/pdfjs/pdf_viewer.html
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:00:34 | INFO     | src.ui.preview_widget:_send_pdf_to_viewer:557 | 已发送PDF加载指令: D:\LocalQA\docs\总经办制度\沪港船办发[2023]047号-关于修订下发《上海港复兴船务有限公司职工就餐管理规定》的通知.pdf, 页码: 3
2025-07-01 09:00:40 | INFO     | src.core.search_system:ai_question:369 | 开始AI问答: 餐费补贴
2025-07-01 09:00:40 | INFO     | src.core.vector_store:search:239 | 语义搜索完成，返回 5 个结果
2025-07-01 09:00:40 | INFO     | src.core.search_system:vector_search:249 | 向量搜索完成: 餐费补贴, 返回 5 个结果
2025-07-01 09:00:40 | INFO     | src.core.ai_model:chat:537 | 提示词token数量: 393
2025-07-01 09:00:40 | INFO     | src.core.ai_model:generate_response:426 | 开始生成回复...
2025-07-01 09:00:41 | INFO     | src.core.ai_model:generate_response:436 | 生成完成，耗时: 1.20秒
2025-07-01 09:00:41 | INFO     | src.core.search_system:ai_question:413 | AI问答完成: 餐费补贴, 耗时 1.27秒
2025-07-01 09:00:41 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:00:51 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:538 | 🔗 来源链接点击: source_1
2025-07-01 09:00:51 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:548 | 📍 解析来源索引: 0
2025-07-01 09:00:51 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:554 | 📝 当前会话消息数量: 4
2025-07-01 09:00:51 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:560 | ✅ 找到AI消息，来源数量: 5
2025-07-01 09:00:51 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:565 | 🎯 显示来源 1: <class 'dict'>
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:load_document:406 | 加载文档: D:\LocalQA\docs\财务部制度\沪港船财发[2024]041号-于修订下发《上海港复兴船务有限公司职工福利费财务管理规定》的通知.pdf, 页码: 3
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:load_document:422 | 检测到PDF文件，尝试直接加载: D:\LocalQA\docs\财务部制度\沪港船财发[2024]041号-于修订下发《上海港复兴船务有限公司职工福利费财务管理规定》的通知.pdf
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:load_document:427 | 使用QWebEngineView直接加载PDF
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:_load_pdf_directly:480 | 使用PDF.js加载PDF: D:\LocalQA\docs\财务部制度\沪港船财发[2024]041号-于修订下发《上海港复兴船务有限公司职工福利费财务管理规定》的通知.pdf
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:_load_pdf_directly:481 | PDF.js查看器路径: file:///D:/LocalQA/static/pdfjs/pdf_viewer.html
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:00:51 | INFO     | src.ui.preview_widget:_send_pdf_to_viewer:557 | 已发送PDF加载指令: D:\LocalQA\docs\财务部制度\沪港船财发[2024]041号-于修订下发《上海港复兴船务有限公司职工福利费财务管理规定》的通知.pdf, 页码: 3
2025-07-01 09:00:53 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:538 | 🔗 来源链接点击: source_2
2025-07-01 09:00:53 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:548 | 📍 解析来源索引: 1
2025-07-01 09:00:53 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:554 | 📝 当前会话消息数量: 4
2025-07-01 09:00:53 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:560 | ✅ 找到AI消息，来源数量: 5
2025-07-01 09:00:53 | INFO     | src.ui.enhanced_qa_interface:_on_source_link_clicked:565 | 🎯 显示来源 2: <class 'dict'>
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:load_document:406 | 加载文档: D:\LocalQA\docs\总经办制度\沪港船办发[2023]046号-关于修订下发《上海港复兴船务有限公司差旅费管理规定》的通知.pdf, 页码: 9
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:load_document:422 | 检测到PDF文件，尝试直接加载: D:\LocalQA\docs\总经办制度\沪港船办发[2023]046号-关于修订下发《上海港复兴船务有限公司差旅费管理规定》的通知.pdf
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:load_document:427 | 使用QWebEngineView直接加载PDF
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:_load_pdf_directly:480 | 使用PDF.js加载PDF: D:\LocalQA\docs\总经办制度\沪港船办发[2023]046号-关于修订下发《上海港复兴船务有限公司差旅费管理规定》的通知.pdf
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:_load_pdf_directly:481 | PDF.js查看器路径: file:///D:/LocalQA/static/pdfjs/pdf_viewer.html
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:00:53 | INFO     | src.ui.preview_widget:_send_pdf_to_viewer:557 | 已发送PDF加载指令: D:\LocalQA\docs\总经办制度\沪港船办发[2023]046号-关于修订下发《上海港复兴船务有限公司差旅费管理规定》的通知.pdf, 页码: 9
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_on_nav_item_clicked:464 | 导航项点击: 💬 对话 07-01 09:00, 类型: session_6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_on_nav_item_clicked:486 | 点击历史会话 - 原始数据: session_6bb992c4-a669-4d68-8882-2c2be30c796e, 提取的会话ID: 6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_switch_to_session:632 | 开始切换到会话: 6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:05 | INFO     | src.core.session_manager:switch_session:175 | 切换到会话: 职工用餐标准
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_switch_to_session:637 | 会话切换结果: True
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_switch_to_session:641 | 刷新会话树
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 职工用餐标准, ID: 6bb992c4-a669-4d68-8882-2c2be30c796e, 数据: session_6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 职工用餐标准
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 业务招待费的标准, ID: cd702d76-26ac-41c4-848d-a1a915a0c149, 数据: session_cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 职工用餐标准
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_ensure_current_session_highlighted:681 | 确保高亮设置: 💬 职工用餐标准
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_switch_to_session:649 | 刷新问答界面
2025-07-01 09:01:05 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 09:01:05 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:01:05 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_switch_to_session:654 | 会话切换成功: 6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:05 | INFO     | src.ui.main_window:_update_status:1041 | 状态更新: 当前页面: 智能问答
2025-07-01 09:01:05 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:01:07 | INFO     | src.core.session_manager:delete_session:193 | 删除会话: cd702d76-26ac-41c4-848d-a1a915a0c149
2025-07-01 09:01:07 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 职工用餐标准, ID: 6bb992c4-a669-4d68-8882-2c2be30c796e, 数据: session_6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:07 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 职工用餐标准
2025-07-01 09:01:07 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 职工用餐标准
2025-07-01 09:01:07 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 09:01:07 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:01:07 | INFO     | src.ui.enhanced_qa_interface:_add_message_to_chat:470 | 🔗 已为消息标签设置链接点击处理，来源数量: 5
2025-07-01 09:01:07 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:01:11 | INFO     | src.core.session_manager:create_new_session:124 | 创建新会话: 对话 07-01 09:01
2025-07-01 09:01:11 | INFO     | src.core.session_manager:delete_session:193 | 删除会话: 6bb992c4-a669-4d68-8882-2c2be30c796e
2025-07-01 09:01:11 | INFO     | src.ui.main_window:_refresh_session_tree:604 | 添加会话项: 对话 07-01 09:01, ID: d32397a4-e2e6-4119-ba83-7b13783d619c, 数据: session_d32397a4-e2e6-4119-ba83-7b13783d619c
2025-07-01 09:01:11 | INFO     | src.ui.main_window:_refresh_session_tree:609 | 标记当前会话项: 对话 07-01 09:01
2025-07-01 09:01:11 | INFO     | src.ui.main_window:_refresh_session_tree:624 | 当前会话高亮设置完成: 💬 对话 07-01 09:01
2025-07-01 09:01:11 | INFO     | src.ui.enhanced_qa_interface:_reset_document_preview:910 | 文档预览区域已重置
2025-07-01 09:01:11 | INFO     | src.ui.preview_widget:_on_pdfjs_load_finished:513 | PDF.js查看器加载成功
2025-07-01 09:01:45 | INFO     | src.ui.main_window:closeEvent:1216 | 应用程序正在关闭...
2025-07-01 09:01:46 | INFO     | src.utils.performance_monitor:stop_monitoring:76 | 性能监控已停止
