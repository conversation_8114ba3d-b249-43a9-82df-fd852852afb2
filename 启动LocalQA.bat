@echo off
chcp 65001 >nul
title LocalQA 本地智能问答系统

echo.
echo ========================================
echo    LocalQA 本地智能问答系统 v1.0
echo ========================================
echo.
echo 正在启动程序，请稍候...
echo.

REM 检查main.exe是否存在
if not exist "main.exe" (
    echo ❌ 错误：找不到 main.exe 文件
    echo 请确保此批处理文件与 main.exe 在同一目录下
    echo.
    pause
    exit /b 1
)

REM 启动程序
echo ✅ 找到程序文件，正在启动...
start "" "main.exe"

REM 等待一下让程序启动
timeout /t 3 /nobreak >nul

echo.
echo ✅ 程序已启动！
echo.
echo 💡 使用提示：
echo    - 首次启动可能需要1-2分钟加载时间
echo    - 如遇问题请查看"使用说明.md"文档
echo    - 或联系技术支持
echo.
echo 按任意键关闭此窗口...
pause >nul
