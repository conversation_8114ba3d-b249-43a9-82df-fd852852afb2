# LocalQA 本地问答系统配置文件
# 版本: 1.0.0
# 说明: 此配置文件针对用户环境进行了优化，确保最佳性能和兼容性

# 模型配置
model:
  # 嵌入模型 - 用于文档向量化
  embedding_model: "shibing624/text2vec-base-chinese"

  # 大语言模型 - 用于问答生成
  llm_model: "qwen-1.5-1.8b-chat"

  # 生成参数
  max_tokens: 1024      # 增加token数量以支持更长回答
  temperature: 0.3      # 降低温度提高准确性
  top_p: 0.8           # 核采样参数
  repetition_penalty: 1.1  # 重复惩罚

  # 设备配置
  device: "auto"        # auto: 自动检测, cpu: 强制CPU, cuda: 强制GPU

  # CPU优化配置
  cpu_optimization:
    num_threads: 4      # CPU线程数，根据用户机器自动调整
    use_mkl: true       # 使用Intel MKL加速
    torch_num_threads: 4 # PyTorch线程数

# 数据库配置
database:
  # ChromaDB向量数据库
  chroma_persist_dir: "data/chroma_db"
  collection_name: "policy_documents"

  # Whoosh全文索引
  whoosh_index_dir: "data/whoosh_index"

  # 检索配置
  max_results: 10       # 最大检索结果数
  similarity_threshold: 0.6  # 相似度阈值

# 文档处理配置
processing:
  # 文本分块配置
  chunk_size: 800       # 增加块大小以保持上下文完整性
  chunk_overlap: 100    # 增加重叠以避免信息丢失

  # 支持的文档格式
  supported_formats:
    - ".pdf"
    - ".docx"
    - ".doc"
    - ".xlsx"
    - ".xls"
    - ".txt"

  # OCR配置
  ocr_language: "chi_sim+eng"
  ocr_enabled: true

  # 文档预处理
  remove_headers_footers: true  # 移除页眉页脚
  merge_paragraphs: true        # 合并段落

# 界面配置
ui:
  # 窗口设置
  window_title: "LocalQA - 本地智能问答系统"
  window_width: 1400    # 增加窗口宽度
  window_height: 900    # 增加窗口高度
  min_width: 1000       # 最小宽度
  min_height: 700       # 最小高度

  # 主题设置
  theme: "light"        # light, dark

  # 字体设置
  font_family: "Microsoft YaHei UI"  # 中文友好字体
  font_size: 10

  # 界面布局
  sidebar_width: 250    # 侧边栏宽度
  preview_width: 400    # 预览面板宽度

# 路径配置 - 支持打包环境
docs_dir: "docs"
data_dir: "data"
models_dir: "models"
preview_dir: "data/preview_pdfs"
static_dir: "static"
logs_dir: "logs"

# 性能配置
performance:
  # 内存管理
  max_memory_gb: 8      # 最大内存使用量
  memory_threshold: 0.8 # 内存使用阈值

  # 缓存配置
  enable_cache: true
  cache_size: 2000      # 增加缓存大小
  cache_ttl: 3600       # 缓存过期时间(秒)

  # 并发配置
  max_concurrent_requests: 3  # 最大并发请求数
  request_timeout: 30         # 请求超时时间(秒)

# 日志配置
logging:
  level: "INFO"         # DEBUG, INFO, WARNING, ERROR
  file: "logs/app.log"
  max_size: "10MB"      # 单个日志文件最大大小
  backup_count: 5       # 保留的日志文件数量
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 安全配置
security:
  # 文件访问限制
  allowed_file_extensions: [".pdf", ".docx", ".doc", ".xlsx", ".xls", ".txt"]
  max_file_size_mb: 100  # 单个文件最大大小

  # 查询限制
  max_query_length: 500  # 最大查询长度
  rate_limit: 60         # 每分钟最大查询次数

# 用户体验配置
user_experience:
  # 响应配置
  show_typing_indicator: true    # 显示输入指示器
  stream_response: false         # 流式响应（当前版本不支持）
  auto_save_sessions: true       # 自动保存会话

  # 界面行为
  auto_scroll: true             # 自动滚动到最新消息
  highlight_sources: true       # 高亮显示来源
  show_confidence_score: false  # 显示置信度分数（当前版本不支持）

# 开发和调试配置（生产环境建议关闭）
debug:
  enabled: false        # 调试模式
  verbose_logging: false # 详细日志
  show_sql_queries: false # 显示SQL查询
  performance_monitoring: true # 性能监控
