# LocalQA 本地问答系统 - 用户使用说明

## 📋 系统概述

LocalQA 是一个基于人工智能的本地文档问答系统，专为企业内部制度文档查询而设计。系统采用先进的AI技术，支持智能问答和精确检索，所有数据完全本地化存储，确保信息安全。

### 🌟 主要功能
- **智能问答**：基于Qwen-1.5-1.8B模型，提供准确的制度问答服务
- **文档检索**：支持关键词搜索，结果按相关性排序
- **文档预览**：支持PDF文档在线预览和定位
- **来源引用**：所有答案都附带准确的来源引用
- **本地部署**：无需联网，数据完全本地化

## 💻 系统要求

### 最低配置要求
- **操作系统**：Windows 10 (64位) 或更高版本
- **内存**：8GB RAM（推荐16GB）
- **存储空间**：10GB 可用磁盘空间
- **处理器**：Intel i5 或 AMD 同等性能处理器

### 推荐配置
- **操作系统**：Windows 11 (64位)
- **内存**：16GB RAM 或更高
- **存储空间**：20GB 可用磁盘空间（SSD推荐）
- **显卡**：NVIDIA GTX 1060 或更高（可选，用于GPU加速）
- **处理器**：Intel i7 或 AMD Ryzen 7

### GPU加速支持（可选）
- 支持CUDA的NVIDIA显卡
- 已安装CUDA 11.8或更高版本
- 显存建议4GB以上

## 📦 安装步骤

### 1. 解压安装包
1. 将收到的 `LocalQA_v1.0.zip` 解压到任意目录
2. 推荐解压到 `D:\LocalQA` 或 `C:\LocalQA`
3. 确保解压路径中不包含中文字符或特殊符号

### 2. 检查文件完整性
解压后应包含以下文件和文件夹：
```
LocalQA/
├── main.exe              # 主程序
├── _internal/            # 程序依赖文件
│   ├── data/            # 数据库文件
│   ├── models/          # AI模型文件
│   ├── static/          # 静态资源
│   └── ...              # 其他依赖
├── 使用说明.md           # 本说明文档
└── config.yaml          # 配置文件（如果存在）
```

### 3. 启动程序
1. 双击 `main.exe` 启动程序
2. 首次启动可能需要1-2分钟加载时间，请耐心等待
3. 看到主界面后即可开始使用

## 🚀 使用方法

### 主界面介绍
程序启动后，您将看到包含以下功能区域的主界面：
- **左侧导航栏**：功能菜单和会话历史
- **中央区域**：问答对话或搜索结果
- **右侧面板**：文档预览区域

### 智能问答功能
1. 点击左侧"智能问答"或"新建对话"
2. 在输入框中输入您的问题
3. 点击发送或按回车键提交问题
4. 系统将基于文档内容给出准确回答
5. 点击答案中的来源引用可预览原始文档

### 文档检索功能
1. 点击左侧"文档检索"
2. 输入关键词进行搜索
3. 查看搜索结果列表
4. 点击结果项可预览完整文档

### 会话管理
- **新建会话**：点击"新建对话"开始新的问答会话
- **历史会话**：左侧显示历史对话记录
- **删除会话**：右键点击会话可删除

## ⚙️ 配置说明

### 设备模式选择
程序支持自动检测和手动选择运行模式：
- **自动模式**：程序自动检测GPU并选择最优运行模式
- **CPU模式**：使用CPU进行推理，兼容性最好
- **GPU模式**：使用NVIDIA GPU加速，响应更快

### 性能优化建议
1. **内存不足时**：关闭其他大型程序，释放内存
2. **响应较慢时**：检查是否有杀毒软件干扰
3. **GPU模式异常时**：切换到CPU模式使用

## 🔧 常见问题解决

### Q1: 程序无法启动
**可能原因**：
- 系统不兼容或缺少运行库
- 杀毒软件误报阻止运行
- 文件损坏或不完整

**解决方法**：
1. 确认系统版本符合要求（Windows 10 64位以上）
2. 将程序添加到杀毒软件白名单
3. 重新解压安装包
4. 以管理员身份运行程序

### Q2: 程序启动后界面空白或卡死
**可能原因**：
- 内存不足
- 模型文件加载失败
- 网络或防火墙干扰

**解决方法**：
1. 关闭其他程序释放内存
2. 检查 `_internal/models/` 文件夹是否完整
3. 关闭防火墙或网络代理
4. 重启程序

### Q3: AI回答不准确或无法回答
**可能原因**：
- 问题超出文档范围
- 文档数据库未正确加载
- 问题表述不够清晰

**解决方法**：
1. 确认问题在现有文档范围内
2. 重新表述问题，使用更具体的关键词
3. 尝试使用文档检索功能查找相关内容

### Q4: 文档预览无法显示
**可能原因**：
- PDF文件损坏
- 预览组件加载失败

**解决方法**：
1. 重启程序
2. 检查 `_internal/static/` 文件夹是否完整
3. 尝试使用其他PDF阅读器打开原文档

### Q5: 程序运行缓慢
**可能原因**：
- 硬件配置不足
- 后台程序占用资源
- 磁盘空间不足

**解决方法**：
1. 关闭不必要的后台程序
2. 清理磁盘空间，确保至少有5GB可用空间
3. 如有NVIDIA显卡，确保GPU模式正常工作

## 📞 技术支持

### 日志文件位置
如遇问题，可查看日志文件获取详细错误信息：
- 日志文件位置：`程序目录/_internal/logs/app.log`

### 联系方式
如需技术支持，请联系：
- **邮箱**：<EMAIL>
- **电话**：400-xxx-xxxx
- **工作时间**：周一至周五 9:00-18:00

### 问题反馈
反馈问题时，请提供以下信息：
1. 操作系统版本
2. 程序版本号
3. 具体错误现象
4. 日志文件内容（如有）
5. 复现步骤

## 📝 更新说明

### 版本 1.0.0
- 初始版本发布
- 支持智能问答和文档检索
- 支持PDF文档预览
- 支持CPU和GPU运行模式

## ⚠️ 注意事项

1. **数据安全**：所有数据完全本地存储，请定期备份重要数据
2. **文件完整性**：请勿删除或移动 `_internal` 文件夹中的任何文件
3. **系统兼容性**：建议在Windows 10/11系统上使用
4. **网络要求**：程序运行无需联网，但首次启动可能需要验证系统组件
5. **杀毒软件**：部分杀毒软件可能误报，请添加到白名单

## 📄 许可协议

本软件仅供内部使用，请遵守相关许可协议。未经授权，不得复制、分发或修改本软件。

---

**LocalQA Team**  
版本：1.0.0  
更新日期：2025年1月
