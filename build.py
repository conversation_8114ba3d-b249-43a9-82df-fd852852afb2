#!/usr/bin/env python3
"""
LocalQA 本地问答系统自动化打包脚本
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any

# 项目信息
PROJECT_NAME = "LocalQA"
VERSION = "1.0.0"
AUTHOR = "LocalQA Team"

# 路径配置
PROJECT_ROOT = Path(__file__).parent
DIST_DIR = PROJECT_ROOT / "dist"
BUILD_DIR = PROJECT_ROOT / "build"
SPEC_FILE = PROJECT_ROOT / "main.spec"
MAIN_FILE = PROJECT_ROOT / "main.py"

# 必要的文件夹
REQUIRED_DIRS = [
    "data",
    "models", 
    "docs",
    "static",
    "src",
    "logs"
]

# 必要的文件
REQUIRED_FILES = [
    "main.py",
    "config.yaml",
    "requirements.txt",
    "README.md"
]

class BuildManager:
    """打包管理器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.errors = []
        self.warnings = []
    
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌"
        }.get(level, "ℹ️")
        
        print(f"[{timestamp}] {prefix} {message}")
        
        if level == "ERROR":
            self.errors.append(message)
        elif level == "WARNING":
            self.warnings.append(message)
    
    def check_environment(self) -> bool:
        """检查构建环境"""
        self.log("检查构建环境...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            self.log(f"Python版本过低: {sys.version}，需要3.8+", "ERROR")
            return False
        
        self.log(f"Python版本: {sys.version}")
        
        # 检查必要文件
        missing_files = []
        for file_path in REQUIRED_FILES:
            if not (PROJECT_ROOT / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.log(f"缺少必要文件: {missing_files}", "ERROR")
            return False
        
        # 检查必要目录
        missing_dirs = []
        for dir_path in REQUIRED_DIRS:
            full_path = PROJECT_ROOT / dir_path
            if not full_path.exists():
                self.log(f"创建缺失目录: {dir_path}", "WARNING")
                full_path.mkdir(parents=True, exist_ok=True)
        
        # 检查PyInstaller
        try:
            result = subprocess.run(
                ["pyinstaller", "--version"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            self.log(f"PyInstaller版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("PyInstaller未安装，正在安装...", "WARNING")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
                self.log("PyInstaller安装成功", "SUCCESS")
            except subprocess.CalledProcessError as e:
                self.log(f"PyInstaller安装失败: {e}", "ERROR")
                return False
        
        return True
    
    def check_dependencies(self) -> bool:
        """检查依赖包"""
        self.log("检查项目依赖...")
        
        try:
            # 使用项目自带的依赖检查器
            sys.path.insert(0, str(PROJECT_ROOT))
            from src.utils.dependency_checker import check_dependencies
            
            if not check_dependencies():
                self.log("依赖检查失败，请运行: pip install -r requirements.txt", "ERROR")
                return False
            
            self.log("所有依赖检查通过", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"依赖检查出错: {e}", "ERROR")
            return False
    
    def clean_build_dirs(self):
        """清理构建目录"""
        self.log("清理构建目录...")
        
        dirs_to_clean = [DIST_DIR, BUILD_DIR]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    self.log(f"已清理: {dir_path}")
                except Exception as e:
                    self.log(f"清理失败 {dir_path}: {e}", "WARNING")
    
    def run_pyinstaller(self) -> bool:
        """运行PyInstaller打包"""
        self.log("开始PyInstaller打包...")
        
        if not SPEC_FILE.exists():
            self.log(f"spec文件不存在: {SPEC_FILE}", "ERROR")
            return False
        
        cmd = [
            "pyinstaller",
            "--noconfirm",
            "--clean",
            str(SPEC_FILE)
        ]
        
        try:
            self.log(f"执行命令: {' '.join(cmd)}")
            
            # 运行打包命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=PROJECT_ROOT
            )
            
            # 实时输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            if process.returncode == 0:
                self.log("PyInstaller打包完成", "SUCCESS")
                return True
            else:
                self.log(f"PyInstaller打包失败，返回码: {process.returncode}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"打包过程出错: {e}", "ERROR")
            return False
    
    def verify_build(self) -> bool:
        """验证打包结果"""
        self.log("验证打包结果...")
        
        # 检查输出目录
        output_dir = DIST_DIR / "main"
        if not output_dir.exists():
            self.log(f"输出目录不存在: {output_dir}", "ERROR")
            return False
        
        # 检查可执行文件
        exe_file = output_dir / "main.exe"
        if not exe_file.exists():
            self.log(f"可执行文件不存在: {exe_file}", "ERROR")
            return False
        
        # 检查必要的数据文件夹
        internal_dir = output_dir / "_internal"
        if not internal_dir.exists():
            self.log(f"内部文件夹不存在: {internal_dir}", "ERROR")
            return False
        
        # 检查数据文件夹
        required_data_dirs = ["data", "models", "static", "src"]
        missing_data_dirs = []
        
        for dir_name in required_data_dirs:
            if not (internal_dir / dir_name).exists():
                missing_data_dirs.append(dir_name)
        
        if missing_data_dirs:
            self.log(f"缺少数据文件夹: {missing_data_dirs}", "ERROR")
            return False
        
        # 计算打包大小
        total_size = sum(
            f.stat().st_size 
            for f in output_dir.rglob('*') 
            if f.is_file()
        )
        size_mb = total_size / (1024 * 1024)
        
        self.log(f"打包大小: {size_mb:.1f} MB", "SUCCESS")
        self.log(f"输出目录: {output_dir}", "SUCCESS")
        
        return True
    
    def create_delivery_package(self) -> bool:
        """创建交付包"""
        self.log("创建交付包...")
        
        output_dir = DIST_DIR / "main"
        if not output_dir.exists():
            self.log("打包输出目录不存在", "ERROR")
            return False
        
        # 创建交付包目录
        package_name = f"{PROJECT_NAME}_v{VERSION}"
        package_dir = DIST_DIR / package_name
        
        if package_dir.exists():
            shutil.rmtree(package_dir)
        
        # 复制主程序
        shutil.copytree(output_dir, package_dir)
        
        # 复制说明文件
        readme_file = PROJECT_ROOT / "用户使用说明.md"
        if readme_file.exists():
            shutil.copy2(readme_file, package_dir / "使用说明.md")
        
        self.log(f"交付包创建完成: {package_dir}", "SUCCESS")
        return True
    
    def generate_report(self):
        """生成构建报告"""
        elapsed_time = time.time() - self.start_time
        
        print("\n" + "="*60)
        print(f"📦 {PROJECT_NAME} v{VERSION} 构建报告")
        print("="*60)
        print(f"⏱️  构建时间: {elapsed_time:.1f}秒")
        print(f"✅ 成功: {len(self.errors) == 0}")
        
        if self.warnings:
            print(f"⚠️  警告数量: {len(self.warnings)}")
            for warning in self.warnings:
                print(f"   - {warning}")
        
        if self.errors:
            print(f"❌ 错误数量: {len(self.errors)}")
            for error in self.errors:
                print(f"   - {error}")
        else:
            print("🎉 构建成功完成！")
            print(f"📁 输出目录: {DIST_DIR}")
        
        print("="*60)
    
    def build(self) -> bool:
        """执行完整构建流程"""
        self.log(f"开始构建 {PROJECT_NAME} v{VERSION}")
        
        # 1. 环境检查
        if not self.check_environment():
            return False
        
        # 2. 依赖检查
        if not self.check_dependencies():
            return False
        
        # 3. 清理构建目录
        self.clean_build_dirs()
        
        # 4. 执行打包
        if not self.run_pyinstaller():
            return False
        
        # 5. 验证结果
        if not self.verify_build():
            return False
        
        # 6. 创建交付包
        if not self.create_delivery_package():
            return False
        
        return True


def main():
    """主函数"""
    print(f"🚀 {PROJECT_NAME} 自动化打包工具")
    print(f"版本: {VERSION}")
    print(f"作者: {AUTHOR}")
    print("-" * 50)
    
    builder = BuildManager()
    
    try:
        success = builder.build()
        builder.generate_report()
        
        if success:
            print("\n🎊 打包完成！用户可以直接运行 dist/main/main.exe")
            sys.exit(0)
        else:
            print("\n💥 打包失败，请检查错误信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断构建")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 构建过程出现异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
