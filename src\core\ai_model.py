"""
AI模型管理模块
管理Qwen-1.5-1.8B-Chat模型的加载和推理
"""
import os
import gc
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import time

try:
    import torch
    from transformers import (
        AutoTokenizer,
        AutoModelForCausalLM,
        AutoConfig
    )
    TORCH_AVAILABLE = True
except ImportError as e:
    torch = AutoTokenizer = AutoModelForCausalLM = AutoConfig = None
    TORCH_AVAILABLE = False
except Exception as e:
    # 处理打包环境中的torch导入问题
    torch = AutoTokenizer = AutoModelForCausalLM = AutoConfig = None
    TORCH_AVAILABLE = False

from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import get_optimal_device, get_memory_usage, retry_on_exception, circuit_breaker
from ..utils.exceptions import ModelLoadError, ResourceError, ValidationError

logger = get_logger(__name__)


class AIModel:
    """AI模型管理器"""

    def __init__(self) -> None:
        self.config = get_config()
        self.model: Optional[Any] = None
        self.tokenizer: Optional[Any] = None
        self.model_loaded: bool = False

        # 检查torch是否可用
        if not TORCH_AVAILABLE:
            logger.warning("Torch不可用，AI模型功能将受限")
            self.device = "cpu"
            self.model_name = "mock"
            self.max_tokens = 512
            self.temperature = 0.7
            self.use_4bit = False
            self.max_memory_gb = 8
            return

        try:
            self.device: str = get_optimal_device()
            # 模型配置
            self.model_name: str = self.config.model.llm_model
            self.max_tokens: int = self.config.model.max_tokens
            self.temperature: float = self.config.model.temperature

            # 性能配置
            self.max_memory_gb: int = self.config.max_memory_gb
        except Exception as e:
            logger.error(f"AI模型初始化失败: {e}")
            # 使用默认配置
            self.device = "cpu"
            self.model_name = "mock"
            self.max_tokens = 512
            self.temperature = 0.7
            self.max_memory_gb = 8

    def set_device(self, device: str):
        """设置设备"""
        self.device = device
        logger.info(f"AI模型设备已设置为: {device}")

        # 设备切换完成
        logger.info(f"设备切换完成: {device}")

    def _setup_gpu_optimization(self):
        """设置GPU优化参数"""
        if torch is not None and torch.cuda.is_available():
            # 启用CUDA优化
            torch.backends.cudnn.enabled = True
            torch.backends.cudnn.benchmark = True
            logger.info("GPU优化配置已启用")







    def _get_model_path(self) -> Path:
        """获取模型路径"""
        model_path = Path(self.config.models_dir) / self.model_name
        return model_path
    
    def _check_model_exists(self) -> bool:
        """检查模型是否存在"""
        model_path = self._get_model_path()
        
        # 检查必要的模型文件
        required_files = ['config.json']
        optional_files = ['pytorch_model.bin', 'model.safetensors']
        
        # 检查配置文件
        if not (model_path / 'config.json').exists():
            return False
        
        # 检查模型权重文件
        has_weights = any((model_path / f).exists() for f in optional_files)
        if not has_weights:
            # 检查分片模型文件
            has_sharded = any(model_path.glob('pytorch_model-*.bin'))
            if not has_sharded:
                return False
        
        return True
    



    
    def load_model(self, force_reload: bool = False) -> bool:
        """加载模型"""
        if self.model_loaded and not force_reload:
            logger.info("模型已加载")
            return True

        if not TORCH_AVAILABLE:
            logger.warning("Torch不可用，使用模拟模型")
            self.model_loaded = True
            return True

        if torch is None:
            raise ModelLoadError("PyTorch未安装，无法加载模型", model_name=self.model_name)

        try:
            logger.info(f"开始加载AI模型: {self.model_name}")
            start_time = time.time()

            model_path = self._get_model_path()

            # 检查模型是否存在
            if not self._check_model_exists():
                raise ModelLoadError(
                    f"模型文件不存在: {model_path}，请确保模型文件已下载到models目录",
                    model_name=self.model_name,
                    model_path=str(model_path)
                )

            # 检查内存
            memory_info = get_memory_usage()
            logger.info(f"当前内存使用: {memory_info['rss_mb']:.1f}MB ({memory_info['percent']:.1f}%)")

            # 检查内存是否足够
            if memory_info['percent'] > 85:
                raise ResourceError(
                    f"内存使用率过高: {memory_info['percent']:.1f}%，无法加载模型",
                    resource_type="memory",
                    current_usage=memory_info['percent'],
                    limit=85.0
                )



            # 加载tokenizer
            logger.info("加载tokenizer...")
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    str(model_path),
                    trust_remote_code=True,
                    local_files_only=True
                )
            except Exception as e:
                raise ModelLoadError(f"Tokenizer加载失败: {e}", model_name=self.model_name)

            # 加载模型
            logger.info("加载模型...")
            load_kwargs = {
                'pretrained_model_name_or_path': str(model_path),
                'trust_remote_code': True,
                'local_files_only': True,
                'low_cpu_mem_usage': True
            }

            # 设备和精度配置
            if self.device == 'cuda':
                load_kwargs.update({
                    'torch_dtype': torch.float16,
                    'device_map': 'auto'
                })
            else:
                load_kwargs.update({
                    'torch_dtype': torch.float32,
                    'device_map': {'': 'cpu'}
                })

            # 加载模型
            try:
                self.model = AutoModelForCausalLM.from_pretrained(**load_kwargs)
            except Exception as e:
                raise ModelLoadError(f"模型权重加载失败: {e}", model_name=self.model_name)



            # 设置评估模式
            self.model.eval()



            # 检查加载后的内存
            memory_info_after = get_memory_usage()
            memory_used = memory_info_after['rss_mb'] - memory_info['rss_mb']

            load_time = time.time() - start_time

            logger.info(f"模型加载成功！")
            logger.info(f"加载时间: {load_time:.2f}秒")
            logger.info(f"内存增加: {memory_used:.1f}MB")
            logger.info(f"设备: {self.device}")

            # 模型预热，避免首次生成异常
            self._warmup_model()

            self.model_loaded = True
            return True

        except (ModelLoadError, ResourceError):
            # 重新抛出自定义异常
            self._cleanup_model()
            raise
        except FileNotFoundError as e:
            self._cleanup_model()
            raise ModelLoadError(f"模型文件未找到: {e}", model_name=self.model_name)
        except ImportError as e:
            self._cleanup_model()
            raise ModelLoadError(f"缺少必要的依赖包: {e}", model_name=self.model_name)
        except RuntimeError as e:
            self._cleanup_model()
            raise ModelLoadError(f"模型运行时错误: {e}", model_name=self.model_name)
        except Exception as e:
            self._cleanup_model()
            raise ModelLoadError(f"模型加载失败: {e}", model_name=self.model_name)
    
    def _warmup_model(self):
        """模型预热，避免首次生成异常"""
        try:
            logger.info("开始模型预热...")
            # 使用简单的测试输入进行预热
            warmup_prompt = "测试"
            encoded = self.tokenizer(warmup_prompt, return_tensors="pt", padding=True, truncation=True)
            inputs = encoded['input_ids']
            attention_mask = encoded.get('attention_mask', None)

            if self.device == 'cuda':
                inputs = inputs.to(self.device)
                if attention_mask is not None:
                    attention_mask = attention_mask.to(self.device)

            # 进行一次简单的生成
            with torch.no_grad():
                if attention_mask is not None:
                    _ = self.model.generate(
                        inputs,
                        attention_mask=attention_mask,
                        max_new_tokens=5,
                        do_sample=False,
                        num_beams=1,
                        pad_token_id=self.tokenizer.eos_token_id,
                        eos_token_id=self.tokenizer.eos_token_id,
                        # 禁用所有随机性参数以避免警告
                        temperature=None,
                        top_p=None,
                        top_k=None,
                    )
                else:
                    _ = self.model.generate(
                        inputs,
                        max_new_tokens=5,
                        do_sample=False,
                        num_beams=1,
                        pad_token_id=self.tokenizer.eos_token_id,
                        eos_token_id=self.tokenizer.eos_token_id,
                        # 禁用所有随机性参数以避免警告
                        temperature=None,
                        top_p=None,
                        top_k=None,
                    )

            logger.info("模型预热完成")
        except Exception as e:
            logger.warning(f"模型预热失败，但不影响正常使用: {e}")

    def _cleanup_model(self):
        """清理模型"""
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
        
        self.model_loaded = False
        
        # 清理GPU缓存
        if torch is not None and torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 强制垃圾回收
        gc.collect()
    
    @retry_on_exception(
        max_retries=2,
        delay=1.0,
        exceptions=(RuntimeError, torch.cuda.OutOfMemoryError if torch else Exception),
        on_retry=lambda attempt, error: logger.warning(f"生成回复重试 {attempt + 1}: {error}")
    )
    def generate_response(self, prompt: str,
                         max_length: Optional[int] = None,
                         temperature: Optional[float] = None) -> str:
        """生成回复"""
        # 检查torch是否可用
        if not TORCH_AVAILABLE:
            logger.warning("Torch不可用，返回模拟响应")
            return f"抱歉，AI模型当前不可用。您的问题是：{prompt[:100]}{'...' if len(prompt) > 100 else ''}\n\n这是一个模拟响应，实际的AI功能需要正确配置的模型环境。"

        # 输入验证
        if not isinstance(prompt, str):
            raise ValidationError("提示词必须是字符串", field_name="prompt", field_value=str(type(prompt)))

        if not prompt.strip():
            raise ValidationError("提示词不能为空", field_name="prompt", field_value=prompt)

        if len(prompt) > 10000:
            raise ValidationError("提示词过长", field_name="prompt", field_value=f"长度: {len(prompt)}")

        if not self.model_loaded:
            try:
                self.load_model()
            except ModelLoadError:
                raise ModelLoadError("模型未加载且加载失败，无法生成回复", model_name=self.model_name)

        try:
            # 检查内存使用情况
            memory_info = get_memory_usage()
            if memory_info['percent'] > 90:
                logger.warning(f"内存使用率过高: {memory_info['percent']:.1f}%，尝试清理缓存")
                self._cleanup_memory()

            # 获取设备优化配置
            try:
                from .device_manager import get_device_manager
                device_manager = get_device_manager()
                perf_config = device_manager.get_performance_config(self.device)
            except:
                perf_config = {}

            # 参数验证和设置 - 使用设备优化配置
            max_length = max_length or perf_config.get('max_tokens', self.max_tokens)
            temperature = temperature or perf_config.get('temperature', self.temperature)

            if max_length <= 0 or max_length > 8192:
                raise ValidationError("max_length必须在1-8192之间", field_name="max_length", field_value=max_length)

            if temperature < 0 or temperature > 2:
                raise ValidationError("temperature必须在0-2之间", field_name="temperature", field_value=temperature)

            # 编码输入，包含attention_mask
            try:
                encoded = self.tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
                inputs = encoded['input_ids']
                attention_mask = encoded.get('attention_mask', None)
            except Exception as e:
                raise ValidationError(f"输入编码失败: {e}", field_name="prompt")

            # 检查输入长度 - 根据设备调整限制，确保回答不被截断
            if self.device == 'cpu':
                # CPU模式：平衡输入和输出长度
                max_input_tokens = 2000  # 允许更长输入
                max_new_tokens = 300     # 进一步增加生成长度
            else:
                # GPU模式：更大的生成空间
                max_input_tokens = max_length - 400  # 预留更多生成空间
                max_new_tokens = min(self.max_tokens, 400)  # 大幅增加生成长度

            if inputs.shape[1] > max_input_tokens:
                # 尝试截断输入而不是直接报错
                logger.warning(f"输入过长({inputs.shape[1]} tokens)，截断到{max_input_tokens} tokens")
                inputs = inputs[:, :max_input_tokens]

            # 移动到设备
            if self.device == 'cuda':
                inputs = inputs.to(self.device)
                if attention_mask is not None:
                    attention_mask = attention_mask.to(self.device)

            # 生成回复 - 使用设备优化参数
            generation_kwargs = {
                'pad_token_id': self.tokenizer.eos_token_id,
                'eos_token_id': self.tokenizer.eos_token_id
            }

            # 设置生成参数 - 平衡长度和质量
            generation_kwargs.update({
                'max_new_tokens': min(max_new_tokens, 80),   # 适当增加长度，确保回答完整
                'do_sample': False,   # 禁用采样，使用贪婪解码
                'num_beams': 1,       # 单束搜索，最大确定性
                'repetition_penalty': 1.0,  # 移除重复惩罚以避免随机性
                'use_cache': True,
                'pad_token_id': self.tokenizer.eos_token_id,
                'eos_token_id': self.tokenizer.eos_token_id,  # 明确设置结束token
                # 显式禁用所有随机性参数
                'temperature': None,
                'top_p': None,
                'top_k': None,
                'typical_p': None,
                'diversity_penalty': 0.0,
                'length_penalty': 1.0,  # 无长度偏好
            })

            # 生成回复 - 添加时间监控
            logger.info("开始生成回复...")
            start_time = time.time()

            with torch.no_grad():
                if attention_mask is not None:
                    outputs = self.model.generate(inputs, attention_mask=attention_mask, **generation_kwargs)
                else:
                    outputs = self.model.generate(inputs, **generation_kwargs)

            generation_time = time.time() - start_time
            logger.info(f"生成完成，耗时: {generation_time:.2f}秒")

            # 解码输出，只获取新生成的部分
            input_length = inputs.shape[1]
            generated_tokens = outputs[0][input_length:]
            response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)

            # 验证输出
            if not response:
                logger.warning("模型生成了空回复")
                return "抱歉，无法生成有效回复。"

            return response

        except torch.cuda.OutOfMemoryError as e:
            logger.error(f"GPU内存不足: {e}")
            self._cleanup_memory()
            raise ResourceError(
                "GPU内存不足，请尝试减少输入长度",
                resource_type="gpu_memory"
            )
        except RuntimeError as e:
            logger.error(f"模型运行时错误: {e}")
            raise ModelLoadError(f"模型运行出错: {str(e)}", model_name=self.model_name)
        except (ValidationError, ResourceError, ModelLoadError):
            # 重新抛出自定义异常
            raise
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            raise ModelLoadError(f"生成回复时出错: {str(e)}", model_name=self.model_name)

    def _cleanup_memory(self):
        """清理内存"""
        try:
            if torch is not None and torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            logger.info("内存清理完成")
        except Exception as e:
            logger.warning(f"内存清理失败: {e}")
    
    def chat(self, query: str, context: List[str] = None) -> str:
        """单轮问答接口 - 优化性能，不支持多轮对话"""
        try:
            # 构建简化的对话格式（无历史上下文）
            messages = []

            # 系统提示 - 简化直接
            system_prompt = "你是查询助手。【】标记是重点。直接回答问题，只用文档内容。没有信息就说'根据提供的文档，没有找到相关信息'。"

            messages.append({"role": "system", "content": system_prompt})

            # 构建当前问题的内容
            current_content = []

            # 添加上下文文档（优化单轮对话）
            if context:
                current_content.append("相关文档：")
                max_context_items = 2  # 减少上下文数量以提升速度

                # 根据设备调整上下文长度限制（更激进的优化）
                if self.device == 'cpu':
                    max_context_length = 600  # CPU模式减少上下文长度以提升速度
                else:
                    max_context_length = 400  # GPU模式也适当减少

                for i, ctx in enumerate(context[:max_context_items], 1):
                    # 加强关键词匹配和上下文处理
                    processed_ctx = self._enhance_context_with_keywords(ctx, query, max_context_length)
                    current_content.append(f"{i}. {processed_ctx}")
                current_content.append("")

            # 添加用户问题
            current_content.append(f"问题：{query}")

            messages.append({"role": "user", "content": "\n".join(current_content)})

            # 使用tokenizer的apply_chat_template方法
            if hasattr(self.tokenizer, 'apply_chat_template'):
                prompt = self.tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True
                )
            else:
                # 降级到简单的提示词拼接
                prompt_parts = []
                for msg in messages:
                    if msg["role"] == "system":
                        prompt_parts.append(f"System: {msg['content']}")
                    elif msg["role"] == "user":
                        prompt_parts.append(f"User: {msg['content']}")
                    elif msg["role"] == "assistant":
                        prompt_parts.append(f"Assistant: {msg['content']}")
                prompt_parts.append("Assistant:")
                prompt = "\n\n".join(prompt_parts)

            # 记录提示词长度
            if self.tokenizer:
                try:
                    prompt_tokens = self.tokenizer.encode(prompt)
                    logger.info(f"提示词token数量: {len(prompt_tokens)}")
                except:
                    pass

            # 生成回复
            response = self.generate_response(prompt)

            # 清理回答内容
            cleaned_response = self._clean_response(response)

            return cleaned_response

        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            return f"对话处理出错: {str(e)}"

    def _clean_response(self, response: str) -> str:
        """清理AI回答内容，移除多余的提示词和格式，严格过滤幻觉内容"""
        try:
            if not response:
                return response

            import re
            cleaned = response.strip()

            # 检查是否包含严重幻觉内容（减少误杀）
            hallucination_patterns = [
                # 严重的编造计算公式
                r'计算公式.*为.*\+.*\*',
                r'绩效奖金.*=.*基数.*×.*系数',
                r'年度.*基数.*×.*系数.*×.*比例',
                # 严重的员工数量推测
                r'公司.*有.*\d+.*名.*员工',
                r'员工.*总数.*为.*\d+',
                # 严重的推测性内容
                r'具体.*金额.*取决于.*员工.*的.*职位.*等级.*和.*年度.*绩效.*表现',
                r'根据.*不同.*的.*职位.*等级.*和.*工作.*年限.*来.*确定',
                # 明显错误的数字
                r'工作满3年.*享受.*15天.*年假',
                # 严重的不相关内容
                r'住房公积金.*缴费.*比例.*根据.*国家.*和.*地方.*的.*政策.*规定.*有所不同',
                r'一般.*情况下.*住房公积金.*缴费.*比例.*为.*职工.*工资.*的.*一定.*比例',
            ]

            # 如果包含幻觉内容，直接返回标准回答
            for pattern in hallucination_patterns:
                if re.search(pattern, cleaned):
                    return "根据提供的文档，没有找到相关信息。"

            # 移除Qwen模型的对话格式标记
            # 移除system部分
            cleaned = re.sub(r'^system\s*\n.*?(?=user|assistant|\Z)', '', cleaned, flags=re.DOTALL)

            # 移除user部分（包括历史对话）
            cleaned = re.sub(r'user\s*\n.*?(?=assistant)', '', cleaned, flags=re.DOTALL)

            # 移除assistant标记，只保留回答内容
            cleaned = re.sub(r'^assistant\s*\n', '', cleaned, flags=re.MULTILINE)

            # 移除多个连续的assistant标记（多轮对话的情况）
            cleaned = re.sub(r'assistant\s*\n.*?(?=assistant|\Z)', '', cleaned, flags=re.DOTALL)

            # 如果还有assistant标记，移除最后一个之前的所有内容
            if 'assistant' in cleaned:
                parts = cleaned.split('assistant')
                if len(parts) > 1:
                    cleaned = parts[-1].strip()

            # 移除常见的提示词前缀和不相关内容
            prefixes_to_remove = [
                "你是一个专业的公司制度查询助手",
                "你是公司制度查询助手",
                "请根据提供的制度文档内容",
                "请严格按照以下要求回答",
                "相关制度文档内容：",
                "相关文档：",
                "用户问题：",
                "问题：",
                "回答：",
                "答案：",
                "答案是：",
                "根据提供的制度文档内容，",
                "根据相关制度文档内容，",
                "根据文档内容，",
                "基于提供的信息，",
                "根据上述文档，",
                "根据制度规定，",
                "我是",
                "我来自",
                "作为",
                "很高兴",
                "您好",
                "你好",
            ]

            # 移除前缀
            for prefix in prefixes_to_remove:
                if cleaned.startswith(prefix):
                    cleaned = cleaned[len(prefix):].strip()
                    break

            # 移除文档索引格式
            cleaned = re.sub(r'^\d+\.\s*[^：]*：[^\n]*\n', '', cleaned, flags=re.MULTILINE)

            # 移除"相关文档："及其后的索引内容
            cleaned = re.sub(r'相关文档：.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)

            # 移除问题行
            cleaned = re.sub(r'问题：.*?\n', '', cleaned)

            # 按行过滤，移除不相关内容
            lines = cleaned.split('\n')
            filtered_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 跳过索引行和格式行
                if re.match(r'^\d+\.\s*$', line):
                    continue
                if re.match(r'^文档：.*$', line):
                    continue
                if re.match(r'^内容：.*$', line):
                    continue
                if line in ['system', 'user', 'assistant']:
                    continue

                # 跳过自我介绍和无关的句子
                skip_patterns = [
                    r'^我是.*助手',
                    r'^我来自.*公司',
                    r'^很高兴.*认识',
                    r'^您好.*我是',
                    r'^你好.*我是',
                    r'^作为.*助手',
                    r'^我主要.*负责',
                    r'^我的.*工作',
                    r'^在.*方面',
                    r'^希望.*帮助',
                    r'^请.*告诉我',
                    r'这个.*规定.*是.*基于',
                    r'根据.*法律.*法规',
                    r'明确.*规定.*了',
                    r'因此.*员工.*在.*满足',
                    r'有权.*享受',
                ]

                should_skip = False
                for pattern in skip_patterns:
                    if re.match(pattern, line):
                        should_skip = True
                        break

                if not should_skip:
                    filtered_lines.append(line)

            cleaned = '\n'.join(filtered_lines)

            # 智能句子截断，减少过度截断
            sentences = cleaned.split('。')
            if len(sentences) > 3:  # 只有超过3句才考虑截断
                first_sentence = sentences[0].strip()
                second_sentence = sentences[1].strip() if len(sentences) > 1 else ""
                third_sentence = sentences[2].strip() if len(sentences) > 2 else ""

                # 检查前两句是否已经完整回答了问题
                if self._is_complete_answer(first_sentence + second_sentence):
                    if second_sentence:
                        cleaned = first_sentence + '。' + second_sentence + '。'
                    else:
                        cleaned = first_sentence + '。'
                else:
                    # 保留前三句以确保回答完整
                    if third_sentence:
                        cleaned = first_sentence + '。' + second_sentence + '。' + third_sentence + '。'
                    elif second_sentence:
                        cleaned = first_sentence + '。' + second_sentence + '。'
                    else:
                        cleaned = first_sentence + '。'

            # 移除多余的空行
            cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)

            # 确保不为空
            if not cleaned.strip():
                return "抱歉，无法生成有效回答。"

            return cleaned.strip()

        except Exception as e:
            logger.error(f"清理回答内容失败: {e}")
            return response

    def _is_complete_answer(self, sentence: str) -> bool:
        """检查句子是否是完整的回答"""
        # 检查是否包含完整的回答要素
        complete_patterns = [
            # 时间相关
            (r'天', r'年假|假期|请假'),
            (r'需要', r'申请|提前'),
            (r'提供', r'证明|材料'),
            # 金额相关
            (r'元|美元', r'住宿|餐费|标准'),
            (r'每天', r'元|美元'),
            # 时间相关
            (r'小时', r'工作|上班'),
            (r'时间', r'工作|上班'),
            # 否定回答
            (r'没有找到', r'相关信息'),
            (r'根据.*文档', r'没有'),
        ]

        for pattern1, pattern2 in complete_patterns:
            import re
            if re.search(pattern1, sentence) and re.search(pattern2, sentence):
                return True

        return False

    def _enhance_context_with_keywords(self, context: str, query: str, max_length: int) -> str:
        """加强关键词匹配，优化上下文内容"""
        import re

        # 提取查询中的关键词
        query_keywords = self._extract_query_keywords(query)

        # 如果上下文不长，直接返回
        if len(context) <= max_length:
            return self._highlight_keywords_in_context(context, query_keywords)

        # 按句子分割上下文
        sentences = context.split('。')

        # 为每个句子计算相关性得分
        sentence_scores = []
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            score = self._calculate_sentence_relevance(sentence, query_keywords)
            sentence_scores.append((sentence, score))

        # 按相关性排序
        sentence_scores.sort(key=lambda x: x[1], reverse=True)

        # 选择最相关的句子
        selected_sentences = []
        current_length = 0

        for sentence, score in sentence_scores:
            if score > 0:  # 只选择有关键词匹配的句子
                sentence_with_period = sentence + '。'
                if current_length + len(sentence_with_period) <= max_length:
                    selected_sentences.append(sentence_with_period)
                    current_length += len(sentence_with_period)
                else:
                    break

        # 如果没有找到相关句子，使用原始截断
        if not selected_sentences:
            truncated = context[:max_length]
            last_period = truncated.rfind('。')
            if last_period > max_length * 0.7:
                return truncated[:last_period + 1]
            else:
                return truncated + "..."

        # 组合选中的句子并高亮关键词
        result = ''.join(selected_sentences)
        return self._highlight_keywords_in_context(result, query_keywords)

    def _extract_query_keywords(self, query: str) -> list:
        """从查询中提取关键词"""
        import re

        # 移除标点符号
        clean_query = re.sub(r'[？?！!，,。.、]', '', query)

        # 定义关键词词典，按类别组织
        keyword_categories = {
            # 请假相关
            'leave': ['请假', '年假', '病假', '事假', '婚假', '产假', '陪产假', '假期'],
            # 时间相关
            'time': ['天', '小时', '时间', '提前', '当日', '工作日', '满1年', '满3年', '满5年'],
            # 证明文件
            'document': ['证明', '医院证明', '申请', '申请表', '发票'],
            # 薪酬相关
            'salary': ['工资', '薪酬', '奖金', '绩效奖金', '年终奖', '基本工资', '20%'],
            # 差旅相关
            'travel': ['差旅费', '住宿费', '餐费', '交通费', '出差', '补贴'],
            # 城市等级
            'city': ['一线城市', '二线城市', '三线城市', '国内', '国外'],
            # 金额相关
            'amount': ['元', '美元', '500元', '300元', '200元', '100元', '50元'],
            # 保险相关
            'insurance': ['五险一金', '养老保险', '医疗保险', '住房公积金', '12%', '16%', '8%'],
            # 工作相关
            'work': ['员工', '公司', '制度', '规定', '标准', '管理'],
        }

        # 提取匹配的关键词
        found_keywords = []
        for category, keywords in keyword_categories.items():
            for keyword in keywords:
                if keyword in clean_query:
                    found_keywords.append(keyword)

        # 如果没有找到预定义关键词，尝试提取查询中的名词
        if not found_keywords:
            # 简单的名词提取（长度大于1的词）
            words = clean_query.split()
            for word in words:
                if len(word) > 1:
                    found_keywords.append(word)

        return found_keywords

    def _calculate_sentence_relevance(self, sentence: str, keywords: list) -> int:
        """计算句子与关键词的相关性得分"""
        score = 0
        sentence_lower = sentence.lower()

        for keyword in keywords:
            keyword_lower = keyword.lower()
            if keyword_lower in sentence_lower:
                # 基础得分
                score += 1

                # 如果关键词在句子开头，额外加分
                if sentence_lower.startswith(keyword_lower):
                    score += 1

                # 如果是重要关键词（数字、金额等），额外加分
                if any(char.isdigit() for char in keyword) or keyword in ['元', '天', '%']:
                    score += 2

                # 如果关键词完全匹配（不是部分匹配），额外加分
                import re
                if re.search(r'\b' + re.escape(keyword_lower) + r'\b', sentence_lower):
                    score += 1

        return score

    def _highlight_keywords_in_context(self, context: str, keywords: list) -> str:
        """在上下文中高亮关键词，提升AI理解"""
        if not keywords:
            return context

        # 为关键词添加特殊标记，帮助AI识别重点
        highlighted = context

        # 按关键词长度排序，避免短词覆盖长词
        sorted_keywords = sorted(keywords, key=len, reverse=True)

        for keyword in sorted_keywords:
            if keyword in highlighted:
                # 使用【】标记重点信息
                highlighted = highlighted.replace(keyword, f'【{keyword}】')

        return highlighted  # 返回原始回答
    
    def is_model_available(self) -> bool:
        """检查模型是否可用"""
        return self._check_model_exists()
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        model_path = self._get_model_path()
        
        info = {
            'model_name': self.model_name,
            'model_path': str(model_path),
            'model_exists': self._check_model_exists(),
            'model_loaded': self.model_loaded,
            'device': self.device,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature
        }
        
        # 如果模型已加载，添加更多信息
        if self.model_loaded and self.model is not None:
            try:
                config = self.model.config
                info.update({
                    'vocab_size': getattr(config, 'vocab_size', 'Unknown'),
                    'hidden_size': getattr(config, 'hidden_size', 'Unknown'),
                    'num_layers': getattr(config, 'num_hidden_layers', 'Unknown')
                })
            except Exception:
                pass
        
        return info
    
    def unload_model(self):
        """卸载模型"""
        logger.info("卸载AI模型...")
        self._cleanup_model()
        logger.info("模型已卸载")


# 全局模型实例
_ai_model_instance = None


def get_ai_model() -> AIModel:
    """获取AI模型实例（单例模式）"""
    global _ai_model_instance
    if _ai_model_instance is None:
        _ai_model_instance = AIModel()
    return _ai_model_instance
