# -*- mode: python ; coding: utf-8 -*-
"""
LocalQA 本地问答系统 PyInstaller 打包配置
"""

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent

# 数据文件配置 - 包含所有必要的文件夹
datas = [
    # 核心数据文件夹
    ('data', 'data'),
    ('models', 'models'),
    ('docs', 'docs'),
    ('static', 'static'),
    ('src', 'src'),
    
    # 配置文件
    ('config.yaml', '.'),
    
    # 日志目录（创建空目录）
    ('logs', 'logs') if os.path.exists('logs') else None,
]

# 过滤掉None值
datas = [item for item in datas if item is not None]

# 隐藏导入 - 确保所有必要模块被包含
hiddenimports = [
    # PyQt6 相关
    'PyQt6.QtCore',
    'PyQt6.QtWidgets',
    'PyQt6.QtGui',
    'PyQt6.QtWebEngineWidgets',
    'PyQt6.QtWebEngineCore',
    'PyQt6.sip',
    
    # AI模型相关
    'torch',
    'transformers',
    'sentence_transformers',
    'modelscope',
    
    # 数据库相关
    'chromadb',
    'whoosh',
    'sqlite3',
    
    # 文档处理相关
    'fitz',  # PyMuPDF
    'docx',
    'openpyxl',
    'PIL',
    'pdf2image',
    'pytesseract',
    'unstructured',
    
    # 中文处理
    'jieba',
    'zhconv',
    
    # 工具库
    'numpy',
    'pandas',
    'tqdm',
    'requests',
    'pydantic',
    'psutil',
    'loguru',
    'yaml',
    'dotenv',
    
    # 项目模块
    'src.core.ai_model',
    'src.core.vector_store',
    'src.core.text_index',
    'src.core.document_processor',
    'src.core.search_system',
    'src.ui.main_window',
    'src.ui.chat_widget',
    'src.ui.search_widget',
    'src.ui.preview_widget',
    'src.utils.config',
    'src.utils.logger',
    'src.utils.helpers',
    'src.utils.dependency_checker',
    'src.utils.performance_monitor',
]

# 排除的模块 - 减少打包体积
excludes = [
    # 开发工具
    'pytest',
    'black',
    'flake8',
    'auto_py_to_exe',
    
    # 不需要的torch组件
    'torch.distributed',
    'torch.nn.parallel',
    'torch.multiprocessing',
    
    # 不需要的matplotlib后端
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.backends.backend_tkagg',
    
    # 其他不需要的模块
    'tkinter',
    'unittest',
    'doctest',
    'pdb',
    'profile',
    'cProfile',
]

# 二进制文件配置
binaries = []

# 收集子模块
collect_submodules = [
    'transformers',
    'sentence_transformers',
    'chromadb',
    'whoosh',
    'unstructured',
]

# 收集数据文件
collect_datas = [
    'transformers',
    'sentence_transformers',
    'chromadb',
    'whoosh',
]

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 过滤重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

# 收集所有文件到一个文件夹
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='main'
)
