2025-07-01 10:06:24 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 10:06:24 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 10:06:26 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: True
2025-07-01 10:06:26 | INFO     | src.utils.dependency_checker:_check_torch:252 | 可用GPU数量: 1
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.1.2+cu118)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.22)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.14)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.0)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 10:06:27 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
2025-07-01 10:06:51 | INFO     | src.utils.logger:setup_logger:96 | 日志系统初始化完成
2025-07-01 10:06:51 | INFO     | src.utils.dependency_checker:check_all_dependencies:112 | 开始检查依赖...
2025-07-01 10:06:52 | INFO     | src.utils.dependency_checker:_check_torch:248 | PyTorch CUDA支持: True
2025-07-01 10:06:52 | INFO     | src.utils.dependency_checker:_check_torch:252 | 可用GPU数量: 1
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:287 | === 依赖检查报告 ===
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
CORE 依赖:
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ torch (v2.1.2+cu118)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ transformers (v4.44.2)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ sentence_transformers (v4.1.0)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ chromadb (v0.4.22)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ whoosh (v2.7.4)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
UI 依赖:
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ PyQt6_WebEngine
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
DOCUMENT 依赖:
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ fitz (v1.23.14)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ docx (v1.1.0)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ openpyxl (v3.1.5)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:290 | 
OPTIONAL 依赖:
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ psutil (v7.0.0)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:_generate_report:297 |   ✓ pytesseract (v0.3.13)
2025-07-01 10:06:53 | INFO     | src.utils.dependency_checker:validate_environment:321 | 所有必要依赖已满足
