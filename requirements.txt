# 核心依赖 - 支持Qwen模型
torch>=2.0.0,<2.3.0
transformers>=4.37.0,<4.45.0
sentence-transformers>=4.0.0,<5.0.0
chromadb>=0.4.0,<0.5.0
whoosh>=2.7.4,<2.8.0
modelscope>=1.9.0,<1.30.0

# 文档处理 - 稳定版本
PyMuPDF>=1.23.0,<1.24.0
python-docx>=0.8.11,<1.1.0
openpyxl>=3.1.0,<3.2.0
pytesseract>=0.3.10,<0.4.0
pdf2image>=1.16.0,<1.17.0
Pillow>=10.0.0,<11.0.0
unstructured[pdf]>=0.10.0,<0.11.0

# UI框架 - 稳定版本
PyQt6>=6.4.0,<6.7.0
PyQt6-WebEngine>=6.4.0,<6.7.0

# 中文处理
jieba>=0.42.1,<0.43.0
zhconv>=1.4.3,<1.5.0

# 工具库 - 兼容版本
numpy>=1.24.0,<1.26.0
pandas>=2.0.0,<2.2.0
tqdm>=4.65.0,<4.67.0
requests>=2.31.0,<2.32.0
pydantic>=2.0.0,<2.6.0

# 系统监控
psutil>=5.9.0,<6.0.0

# 日志和配置
loguru>=0.7.0,<0.8.0
pyyaml>=6.0,<7.0
python-dotenv>=1.0.0,<1.1.0

# 打包工具
pyinstaller>=5.13.0,<6.0.0
auto-py-to-exe>=2.40.0,<3.0.0

# 可选GPU加速
# torch-audio  # 如果需要音频处理
# accelerate>=0.20.0,<0.25.0  # 模型加速

# 开发工具
pytest>=7.4.0,<8.0.0
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0
